<template>
  <div>
    <div class="mb-4 flex justify-between items-center">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <slot name="header-actions"></slot>
    </div>
    
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th v-for="column in columns" 
                :key="column.key"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ column.label }}
            </th>
            <th v-if="$slots.actions" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ $t('actions') }}
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="(item, index) in filteredItems" :key="item?.id || index">
            <td v-for="column in columns"
                :key="column.key"
                class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <span v-if="column.key === 'role'"
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="item?.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-green-100 text-green-800'">
                {{ getNestedValue(item, column.key) }}
              </span>
              <span v-else-if="column.key === 'outstanding_balance'"
                    class="font-semibold px-2 py-1 rounded"
                    :class="getOutstandingBalanceClass(item?.outstanding_balance)">
                {{ getNestedValue(item, column.key) }}
              </span>
              <span v-else-if="column.key === 'total_expenses'"
                    class="text-red-600 font-medium">
                {{ getNestedValue(item, column.key) }}
              </span>
              <span v-else-if="column.key === 'total_income'"
                    class="text-green-600 font-medium">
                {{ getNestedValue(item, column.key) }}
              </span>
              <span v-else>
                {{ getNestedValue(item, column.key) }}
              </span>
            </td>
            <td v-if="$slots.actions" class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <slot name="actions" :item="item"></slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div v-if="filteredItems.length === 0" class="text-center py-4 text-gray-500">
      {{ $t('no_data') }}
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'DataTable',
  mixins: [i18nMixin],
  props: {
    title: {
      type: String,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    items: {
      type: Array,
      required: true
    }
  },
  computed: {
    filteredItems() {
      // Filter out any null or undefined items
      return this.items.filter(item => item != null);
    }
  },
  methods: {
    getNestedValue(obj, path) {
      if (!obj || !path) return '';

      // Handle special cases for boolean values
      if (path === 'is_automatic') {
        return obj[path] ? 'Yes' : 'No';
      }

      // Handle amount formatting
      if (path === 'amount') {
        const amount = obj[path];
        return amount ? `₪${parseFloat(amount).toFixed(2)}` : '₪0.00';
      }

      // Handle status formatting
      if (path === 'status') {
        const status = obj[path];
        return status ? status.charAt(0).toUpperCase() + status.slice(1) : '';
      }

      // Handle month formatting
      if (path === 'month') {
        const monthNames = {
          '01': 'January', '02': 'February', '03': 'March', '04': 'April',
          '05': 'May', '06': 'June', '07': 'July', '08': 'August',
          '09': 'September', '10': 'October', '11': 'November', '12': 'December'
        };
        return monthNames[obj[path]] || obj[path];
      }

      // Handle payment method formatting
      if (path === 'payment_method') {
        const methods = {
          'cash': 'Cash',
          'bank_transfer': 'Bank Transfer',
          'check': 'Check'
        };
        return methods[obj[path]] || obj[path];
      }

      // Handle role formatting
      if (path === 'role') {
        const role = obj[path];
        return role ? role.charAt(0).toUpperCase() + role.slice(1) : '';
      }

      // Handle financial value formatting
      if (path === 'total_expenses' || path === 'total_income' || path === 'outstanding_balance') {
        const value = obj[path];
        return value !== undefined ? `₪${parseFloat(value).toFixed(2)}` : '₪0.00';
      }

      // Handle date formatting
      if (path === 'payment_date' || path === 'created_at') {
        const date = obj[path];
        return date ? new Date(date).toLocaleDateString() : '';
      }

      // Handle nested properties like 'expenseType.name'
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : '';
      }, obj);
    },
    getOutstandingBalanceClass(balance) {
      const amount = parseFloat(balance);
      if (amount === 0) {
        return 'bg-green-100 text-green-800'; // Green for zero balance
      } else if (amount > 200) {
        return 'bg-red-100 text-red-800'; // Red for high balance (>200)
      } else if (amount > 0) {
        return 'bg-yellow-100 text-yellow-800'; // Yellow for moderate balance (0-200)
      } else {
        return 'bg-green-100 text-green-800'; // Green for negative balance (credit)
      }
    }
  }
};
</script> 