<template>
  <div>
    <h2 v-if="!isEdit" class="text-xl font-semibold text-gray-900 mb-6">{{ $t('create_user') }}</h2>
    
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div>
        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('name') }}</label>
        <input
          type="text"
          id="name"
          v-model="formData.name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('email') }}</label>
        <input
          type="email"
          id="email"
          v-model="formData.email"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="apartment_number" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('apartment_number') }}</label>
        <input
          type="text"
          id="apartment_number"
          v-model="formData.apartment_number"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('role') }}</label>
        <select
          id="role"
          v-model="formData.role"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="">{{ $t('role') }}</option>
          <option v-if="isSuperAdmin" value="super_admin">{{ $t('super_admin') }}</option>
          <option v-if="isSuperAdmin" value="admin">{{ $t('admin') }}</option>
          <option v-if="!isSuperAdmin" value="neighbor">{{ $t('neighbor') }}</option>
        </select>
      </div>

      <!-- Building field - only show for super admin when role is not super_admin -->
      <div v-if="isSuperAdmin && formData.role && formData.role !== 'super_admin'">
        <label for="building_id" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('building') }}</label>
        <select
          id="building_id"
          v-model="formData.building_id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">{{ $t('building') }}</option>
          <option v-for="building in buildings" :key="building.id" :value="building.id">
            {{ building.name }}
          </option>
        </select>
      </div>

      <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
          {{ $t('password') }} {{ isEdit ? '(leave blank to keep current)' : '' }}
        </label>
        <input
          type="password"
          id="password"
          v-model="formData.password"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          :required="!isEdit"
        />
      </div>

      <div>
        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('confirm_password') }}</label>
        <input
          type="password"
          id="password_confirmation"
          v-model="formData.password_confirmation"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          :required="!isEdit"
        />
      </div>

      <div class="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {{ $t('cancel') }}
        </button>
        <button
          type="submit"
          :disabled="processing"
          class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ processing ? $t('loading') : (isEdit ? $t('edit_user') : $t('create_user')) }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'UserForm',
  mixins: [i18nMixin],
  props: {
    user: {
      type: Object,
      default: () => ({
        name: '',
        email: '',
        apartment_number: '',
        role: '',
        building_id: null,
        password: '',
        password_confirmation: ''
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isSuperAdmin: {
      type: Boolean,
      default: false
    },
    adminBuildingId: {
      type: [Number, String],
      default: null
    },
    buildings: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      processing: false,
      formData: {
        name: '',
        email: '',
        apartment_number: '',
        role: '',
        building_id: null,
        password: '',
        password_confirmation: ''
      }
    };
  },
  watch: {
    user: {
      handler(newUser) {
        if (newUser && this.isEdit) {
          this.formData = {
            name: newUser.name || '',
            email: newUser.email || '',
            apartment_number: newUser.apartment_number || '',
            role: newUser.role || '',
            building_id: newUser.building_id || null,
            password: '',
            password_confirmation: ''
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // Initialize form data if editing
    if (this.isEdit && this.user) {
      this.formData = {
        name: this.user.name || '',
        email: this.user.email || '',
        apartment_number: this.user.apartment_number || '',
        role: this.user.role || '',
        building_id: this.user.building_id || null,
        password: '',
        password_confirmation: ''
      };
    }
  },
  methods: {
    async handleSubmit() {
      this.processing = true;
      try {
        const submitData = {
          ...this.formData
        };

        // Remove empty password fields for edit
        if (this.isEdit && !submitData.password) {
          delete submitData.password;
          delete submitData.password_confirmation;
        }

        // Handle building assignment logic
        if (submitData.role === 'super_admin') {
          submitData.building_id = null;
        } else if (!this.isSuperAdmin && this.adminBuildingId) {
          // Regular admin can only create users in their building
          submitData.building_id = this.adminBuildingId;
        }

        const response = await this.$axios[this.isEdit ? 'put' : 'post'](
          this.isEdit ? `/admin/users/${this.user.id}` : '/admin/users',
          submitData
        );

        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || 'Failed to save user');
      } finally {
        this.processing = false;
      }
    }
  }
};
</script>
