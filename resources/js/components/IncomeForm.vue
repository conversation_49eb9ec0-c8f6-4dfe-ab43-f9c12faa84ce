<template>
  <div>
    <h2 v-if="!isEdit" class="text-xl font-semibold text-gray-900 mb-6">{{ $t('create_income') }}</h2>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div>
        <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('neighbor') }}</label>
        <select
          id="user_id"
          v-model="formData.user_id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="">{{ $t('neighbor') }}</option>
          <option v-for="user in neighbors" :key="user.id" :value="user.id">
            {{ user.name }} ({{ $t('apartment_number') }}: {{ user.apartment_number }})
          </option>
        </select>
      </div>

      <div>
        <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('amount') }}</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="text-gray-500 text-sm">₪</span>
          </div>
          <input
            type="number"
            id="amount"
            v-model="formData.amount"
            class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="0.00"
            step="0.01"
            min="0"
            required
          />
        </div>
      </div>

      <div>
        <label for="payment_date" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('payment_date') }}</label>
        <input
          type="date"
          id="payment_date"
          v-model="formData.payment_date"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
      </div>

      <div>
        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('payment_method') }}</label>
        <select
          id="payment_method"
          v-model="formData.payment_method"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="cash">{{ $t('cash') }}</option>
          <option value="bank_transfer">{{ $t('bank_transfer') }}</option>
          <option value="check">{{ $t('check') }}</option>
        </select>
      </div>

      <div>
        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">{{ $t('notes') }}</label>
        <textarea
          id="notes"
          v-model="formData.notes"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
          :placeholder="$t('notes')"
        ></textarea>
      </div>

      <div class="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {{ $t('cancel') }}
        </button>
        <button
          type="submit"
          :disabled="processing"
          class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ processing ? $t('loading') : (isEdit ? $t('edit_income') : $t('create_income')) }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'IncomeForm',
  mixins: [i18nMixin],
  props: {
    income: {
      type: Object,
      default: () => ({
        user_id: '',
        amount: '',
        payment_date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
        notes: ''
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      processing: false,
      neighbors: [],
      formData: {
        user_id: '',
        amount: '',
        payment_date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
        notes: ''
      }
    };
  },
  watch: {
    income: {
      handler(newIncome) {
        if (newIncome && this.isEdit) {
          this.formData = {
            user_id: newIncome.user_id || '',
            amount: newIncome.amount || '',
            payment_date: newIncome.payment_date || new Date().toISOString().split('T')[0],
            payment_method: newIncome.payment_method || 'cash',
            notes: newIncome.notes || ''
          };
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.fetchNeighbors();
  },
  mounted() {
    // Initialize form data if editing
    if (this.isEdit && this.income) {
      this.formData = {
        user_id: this.income.user_id || '',
        amount: this.income.amount || '',
        payment_date: this.income.payment_date || new Date().toISOString().split('T')[0],
        payment_method: this.income.payment_method || 'cash',
        notes: this.income.notes || ''
      };
    }
  },
  methods: {
    async fetchNeighbors() {
      try {
        const response = await this.$axios.get('/admin/users');
        const allUsers = response.data.data || response.data;
        // Filter neighbors - they will already be filtered by building on the backend
        this.neighbors = allUsers.filter(user => user.role === 'neighbor');
      } catch (error) {
        console.error('Error fetching neighbors:', error);
      }
    },
    async handleSubmit() {
      this.processing = true;
      try {
        const submitData = {
          ...this.formData
        };

        const response = await this.$axios[this.isEdit ? 'put' : 'post'](
          this.isEdit ? `/incomes/${this.income.id}` : '/incomes',
          submitData
        );

        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || 'Failed to save income');
      } finally {
        this.processing = false;
      }
    }
  }
};
</script>
