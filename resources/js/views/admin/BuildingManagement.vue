<template>
  <dashboard-layout :title="$t('building_management')">
    <template #sidebar>
      <div v-if="userLoaded">
        <router-link
          to="/admin/expenses"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          Expenses
        </router-link>
        <router-link
          to="/admin/incomes"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          Incomes
        </router-link>
        <router-link
          to="/admin/users"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          Users
        </router-link>
        <router-link
          v-if="isSuperAdmin"
          to="/admin/buildings"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          Buildings
        </router-link>
        <router-link
          v-if="!isSuperAdmin"
          to="/admin/my-building"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          My Building
        </router-link>
      </div>
      <div v-else class="text-gray-500 text-sm">
        Loading menu...
      </div>
    </template>

    <div v-if="loading && !userLoaded" class="text-center py-4">
      <p>Loading...</p>
    </div>

    <div v-else-if="isSuperAdmin">


      <!-- Header with Add Button -->
      <div class="mb-6 flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">{{ $t('building_management') }}</h2>
        <router-link
          to="/admin/buildings/create"
          class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Add Building
        </router-link>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">Total Buildings</h3>
          <p class="text-3xl font-bold text-blue-600">{{ totalBuildings }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">Active Buildings</h3>
          <p class="text-3xl font-bold text-green-600">{{ activeBuildings }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
          <h3 class="text-lg font-semibold text-gray-700">Average Monthly Fee</h3>
          <p class="text-3xl font-bold text-purple-600">₪{{ averageMonthlyFee.toFixed(2) }}</p>
        </div>
      </div>

      <!-- Buildings Table -->
      <data-table
        title="Buildings"
        :columns="columns"
        :items="buildings"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex space-x-2">
            <button
              @click="editBuilding(item)"
              class="text-indigo-600 hover:text-indigo-900"
            >
              Edit
            </button>
            <button
              @click="deleteBuilding(item)"
              class="text-red-600 hover:text-red-900"
            >
              Delete
            </button>
          </div>
        </template>
        <template #header-actions>
          <router-link
            to="/admin/buildings/create"
            class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Building
          </router-link>
        </template>
      </data-table>
    </div>

    <!-- Edit Modal -->
    <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
      <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
        <div class="flex justify-between items-center p-6 border-b">
          <h2 class="text-xl font-semibold text-gray-900">Edit Building</h2>
          <button @click="closeEditModal" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Modal body -->
        <div class="p-6">
          <building-form
            :building="selectedBuilding"
            :is-edit="true"
            :is-my-building="false"
            @success="handleEditSuccess"
            @error="handleEditError"
            @cancel="closeEditModal"
          />
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />

    <div v-if="!isSuperAdmin && userLoaded" class="text-center py-8">
      <p class="text-red-500">You do not have permission to access this page.</p>
    </div>
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import DataTable from '../../components/DataTable.vue';
import BuildingForm from '../../components/BuildingForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    DataTable,
    BuildingForm,
    Notification
  },
  data() {
    return {
      loading: false,
      buildings: [],
      selectedBuilding: null,
      showEditModal: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      user: null,
      isSuperAdmin: false,
      userLoaded: false,
      columns: [
        { key: 'name', label: 'Building Name' },
        { key: 'city', label: 'City' },
        { key: 'monthly_fee_display', label: 'Monthly Fee' },
        { key: 'admin_count', label: 'Admins' },
        { key: 'neighbor_count', label: 'Neighbors' }
      ]
    };
  },
  computed: {
    totalBuildings() {
      return this.buildings.length;
    },
    activeBuildings() {
      return this.buildings.filter(b => b.admin_count > 0).length;
    },
    averageMonthlyFee() {
      if (this.buildings.length === 0) return 0;
      const total = this.buildings.reduce((sum, building) =>
        sum + parseFloat(building.monthly_fee || 0), 0);
      return total / this.buildings.length;
    }
  },
  async mounted() {
    this.initializeUserFromStorage();
    await this.fetchUser();
    if (this.isSuperAdmin) {
      this.loadBuildings();
    }
  },
  methods: {
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.userLoaded = true;
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (error) {
        console.error('Error fetching user:', error);
        this.$router.push('/login');
      }
    },
    async loadBuildings() {
      this.loading = true;
      try {
        // Load buildings
        const buildingsResponse = await this.$axios.get('/buildings');

        // Load users to calculate counts
        const usersResponse = await this.$axios.get('/admin/users');
        const allUsers = usersResponse.data.data || usersResponse.data;

        this.buildings = buildingsResponse.data.map(building => ({
          ...building,
          monthly_fee_display: `₪${parseFloat(building.monthly_fee || 0).toFixed(2)}`,
          admin_count: allUsers.filter(user =>
            user.building_id === building.id && user.role === 'admin'
          ).length,
          neighbor_count: allUsers.filter(user =>
            user.building_id === building.id && user.role === 'neighbor'
          ).length
        }));
      } catch (error) {
        console.error('Error loading buildings:', error);
        this.showError('Error', 'Failed to load buildings');
      } finally {
        this.loading = false;
      }
    },
    editBuilding(building) {
      this.selectedBuilding = building;
      this.showEditModal = true;
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedBuilding = null;
    },
    handleEditSuccess(data) {
      this.showSuccess('Success', 'Building updated successfully');
      this.closeEditModal();
      this.loadBuildings();
    },
    handleEditError(message) {
      this.showError('Error', message);
    },
    async deleteBuilding(building) {
      if (confirm(`Are you sure you want to delete building "${building.name}"?`)) {
        try {
          await this.$axios.delete(`/buildings/${building.id}`);
          this.showSuccess('Success', 'Building deleted successfully');
          this.loadBuildings();
        } catch (error) {
          this.showError('Error', error.response?.data?.message || 'Failed to delete building');
        }
      }
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
