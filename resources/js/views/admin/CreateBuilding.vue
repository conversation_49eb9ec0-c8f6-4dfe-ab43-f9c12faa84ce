<template>
  <dashboard-layout :title="$t('create_building')">
    <template #sidebar>
      <div v-if="userLoaded">
        <router-link
          to="/admin/expenses"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          Expenses
        </router-link>
        <router-link
          to="/admin/incomes"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          Incomes
        </router-link>
        <router-link
          to="/admin/users"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          Users
        </router-link>
        <router-link
          v-if="isSuperAdmin"
          to="/admin/buildings"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          Buildings
        </router-link>
        <router-link
          v-if="!isSuperAdmin"
          to="/admin/my-building"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          My Building
        </router-link>
      </div>
      <div v-else class="text-gray-500 text-sm">
        Loading menu...
      </div>
    </template>

    <div class="max-w-2xl mx-auto">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <building-form
          @success="handleSuccess"
          @error="handleError"
          @cancel="handleCancel"
        />
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import BuildingForm from '../../components/BuildingForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    BuildingForm,
    Notification
  },
  mounted() {
    this.initializeUserFromStorage();
  },
  data() {
    return {
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      userLoaded: false,
      isSuperAdmin: false
    };
  },
  methods: {
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    handleSuccess(data) {
      this.showSuccess('Success', 'Building created successfully');
      setTimeout(() => {
        this.$router.push('/admin/buildings');
      }, 1500);
    },
    handleError(message) {
      this.showError('Error', message);
    },
    handleCancel() {
      this.$router.push('/admin/buildings');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
